import React, { useCallback, useEffect, useState } from "react";
import { UserProfile } from "@/types/user";
import useEmblaCarousel from "embla-carousel-react";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface ReviewsSectionProps {
  profile: UserProfile;
}

const ReviewsSection = ({ profile }: ReviewsSectionProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: true,
  });

  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);
  }, [emblaApi, onSelect]);

  // Check if reviews are enabled and exist
  if (!profile.reviews?.enabled || !profile.reviews?.reviews?.length) {
    return null;
  }

  const reviews = profile.reviews;

  return (
    <section className="py-16 sm:py-24 bg-black" id="reviews">
      <div className="mx-auto max-w-full flex flex-col items-center">
        <div className="text-center mb-12 px-4">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {reviews.title || "Avaliações"}
          </h2>
          {reviews.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {reviews.description}
            </p>
          )}
        </div>

        <div className="relative">
          <div className="overflow-hidden rounded-xl" ref={emblaRef}>
            <div className="flex gap-4 lg:gap-6">
              {reviews.reviews.map((review, index) => (
                <div
                  key={review.id}
                  className="flex-[0_0_85%] sm:flex-[0_0_75%] md:flex-[0_0_65%] lg:flex-[0_0_360px] min-w-0"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="bg-custom rounded-3xl p-6 h-full flex flex-col border border-gray-950 transition-all duration-300 hover:scale-[1.02] transform-gpu">
                    <div className="flex items-start space-x-4 mb-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2 mb-2">
                          <h4 className="font-semibold text-white text-base truncate">
                            {review.name}
                          </h4>
                          <div
                            className="flex text-yellow-400 shrink-0"
                            aria-label={`${review.rating} estrelas`}
                          >
                            {[...Array(review.rating)].map((_, i) => (
                              <i
                                key={i}
                                className="fas fa-star text-sm transition-transform duration-300"
                                style={{ animationDelay: `${i * 0.1}s` }}
                                aria-hidden="true"
                              ></i>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Review comment */}
                    <blockquote className="flex-1 flex items-center">
                      <p className="text-gray-300 text-sm leading-relaxed italic">
                        &ldquo;{review.comment}&rdquo;
                      </p>
                    </blockquote>

                    {/* Decorative quote mark */}
                    <div className="mt-4 flex justify-end opacity-20 transition-opacity duration-300">
                      <i className="fas fa-quote-right text-2xl text-gray-500"></i>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation buttons */}
          {reviews.reviews.length > 1 && (
            <div className="flex justify-center mt-8 gap-3">
              <button
                onClick={scrollPrev}
                disabled={!canScrollPrev}
                className="w-12 h-12 rounded-full bg-gray-800 text-white border border-gray-700 hover:bg-gray-700 transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform flex items-center justify-center"
                aria-label="Avaliação anterior"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <button
                onClick={scrollNext}
                disabled={!canScrollNext}
                className="w-12 h-12 rounded-full bg-gray-800 text-white border border-gray-700 hover:bg-gray-700 transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform flex items-center justify-center"
                aria-label="Próxima avaliação"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          )}

          {/* Progress indicator */}
          {reviews.reviews.length > 1 && (
            <div className="flex justify-center mt-4 gap-2">
              {reviews.reviews.map((_, index) => (
                <div
                  key={index}
                  className="w-2 h-2 rounded-full transition-all duration-300 cursor-pointer bg-white"
                  style={{
                    opacity: selectedIndex === index ? 1 : 0.4,
                    transform:
                      selectedIndex === index ? "scale(1.25)" : "scale(1)",
                  }}
                  onMouseEnter={(e) => {
                    if (selectedIndex !== index) {
                      e.currentTarget.style.opacity = "0.6";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (selectedIndex !== index) {
                      e.currentTarget.style.opacity = "0.4";
                    }
                  }}
                  onClick={() => emblaApi?.scrollTo(index)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default ReviewsSection;
