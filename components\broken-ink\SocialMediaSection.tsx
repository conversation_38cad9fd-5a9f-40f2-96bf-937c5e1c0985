import React from "react";
import SocialCard from "./SocialCard";
import { getIconComponent } from "@/lib/iconUtils";
import { UserProfile } from "@/types/user";
import { transformSocialMediaData } from "@/lib/brokenInkUtils";

interface SocialMediaSectionProps {
  profile: UserProfile;
}

const SocialMediaSection = ({ profile }: SocialMediaSectionProps) => {
  const socialData = transformSocialMediaData(profile);

  return (
    <section id="socialinks" className="py-16 bg-black sm:py-20">
      <div className="container mx-auto px-4 space-y-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            {socialData.title}
          </h2>
          <p className="mt-4 text-lg leading-8 text-gray-300">
            {socialData.description}
          </p>
        </div>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {socialData.socialPlatforms.map((platform) => {
            const IconComponent = getIconComponent(platform.iconName);
            return (
              <SocialCard
                key={platform.platformName}
                icon={<IconComponent className="h-6 w-6 text-white" />}
                platformName={platform.platformName}
                description={platform.description}
                href={platform.href}
              />
            );
          })}
        </div>
        <div className="flex justify-center">
          <button
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-8 bg-white text-black text-base font-bold leading-normal tracking-wide transition-transform hover:scale-105"
            style={
              socialData.colors?.primary
                ? {
                    backgroundColor: socialData.colors.primary,
                    color: socialData.colors.linkText || "#ffffff",
                  }
                : {}
            }
          >
            <span className="truncate">Ver todos os Links</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default SocialMediaSection;
